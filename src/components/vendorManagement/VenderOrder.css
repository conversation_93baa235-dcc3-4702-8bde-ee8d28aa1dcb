/* VendorOrder.css */

.vendor-order-container {
  max-width: 1800px;
  margin: 32px auto;
  margin-left: 280px; /* Add left margin for expanded sidebar */
  padding: 24px;
  background: #fafbfc;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.07);
  display: flex;
  flex-direction: column;
  gap: 32px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  transition: margin-left 0.3s ease; /* Smooth transition when sidebar toggles */
}

/* Adjust margin when sidebar is collapsed */
.vendor-order-container.sidebar-collapsed {
  margin-left: 100px; /* Smaller margin for collapsed sidebar */
}

.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 24px 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
}

.top-bar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 8px !important;
  padding: 8px 16px !important;
  height: auto !important;
  font-weight: 500;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  color: white !important;
}

.order-header-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.order-title {
  color: white;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.order-customer {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 400;
}

.button-group {
  display: flex;
  gap: 16px;
}

.reject-button {
  background: #fff;
  color: #ef4444;
  border: 1.5px solid #ef4444;
  border-radius: 6px;
  padding: 8px 24px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}

.reject-button:hover {
  background: #ef4444;
  color: #fff;
}

.approve-button {
  background: #9e3ca2;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 24px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}

.approve-button:hover {
  background: #22c55e;
}

.main-row {
  display: flex;
  gap: 24px;
}

.price-details-section {
  flex: 2;
  min-width: 0;
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #9e3ca2;
  margin-bottom: 12px;
}

.order-date {
  color: #64748b;
  font-size: 0.95rem;
}

.radio-group {
  display: flex;
  gap: 24px;
  margin-bottom: 8px;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.price-inputs {
  display: flex;
  gap: 24px;
  margin-bottom: 8px;
}

.price-input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.price-label {
  font-size: 0.95rem;
  color: #64748b;
}

.price-input {
  border: 1.5px solid #22223b;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 1rem;
  background: #fff;
  color: #22223b;
  width: 120px;
}

.message-textarea {
  width: 100%;
  min-height: 48px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  padding: 8px;
  font-size: 1rem;
  resize: vertical;
  font-family: inherit;
}

.message-center-section {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.messages-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 8px;
  max-height: 180px;
  overflow-y: auto;
}

.message-bubble {
  display: flex;
  flex-direction: column;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 0.98rem;
  color: #22223b;
  max-width: 90%;
}

.message-customer {
  background: #e0e7ff;
  align-self: flex-start;
}

.message-vendor {
  background: #d1fae5;
  align-self: flex-end;
}

.message-time {
  font-size: 0.8rem;
  color: #64748b;
  margin-top: 2px;
  text-align: right;
}

.message-input-container {
  display: flex;
  gap: 8px;
}

.message-input {
  flex: 1;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  padding: 8px;
  font-size: 1rem;
}

.send-button {
  background: #9e3ca2;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 0 16px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: background 0.2s;
}

.send-button:hover {
  background: #22c55e;
}

.details-row {
  display: flex;
  gap: 24px;
}

.details-section {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 350px;
}

.timeline-header {
  font-size: 1.1rem;
  font-weight: 600;
  color: #9e3ca2;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-badge {
  background: #e0e7ff;
  color: #6366f1;
  padding: 2px 10px;
  border-radius: 8px;
  font-size: 0.95rem;
}

.detail-item {
  font-size: 1rem;
  color: #22223b;
  margin-bottom: 6px;
  display: flex;
  gap: 8px;
}

.detail-label {
  flex: 0 0 160px;
  font-weight: 500;
  color: #64748b;
  text-align: right;
}

.detail-value {
  flex: 1;
  color: #22223b;
  text-align: left;
}

.detail-value-pending {
  color: #f59e42;
  font-weight: 500;
}

.timeline-item {
  font-size: 1rem;
  color: #22223b;
  margin-bottom: 6px;
  display: flex;
  gap: 8px;
}

.timeline-label {
  font-weight: 500;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 12px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-title {
  margin: 0 0 16px 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #22223b;
}

.modal-message {
  margin: 0 0 24px 0;
  font-size: 1rem;
  color: #64748b;
  line-height: 1.5;
}

.modal-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.modal-cancel-button {
  background: #fff;
  color: #64748b;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 8px 20px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;
}

.modal-cancel-button:hover {
  background: #f8fafc;
}

.modal-confirm-button {
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 20px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: opacity 0.2s;
}

.modal-confirm-button:hover {
  opacity: 0.9;
}

.modal-confirm-approve {
  background: #9e3ca2;
}

.modal-confirm-reject {
  background: #ef4444;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .vendor-order-container {
    margin-left: 24px; /* Reset margin on smaller screens */
    margin-right: 24px;
  }
  
  .vendor-order-container.sidebar-collapsed {
    margin-left: 24px;
  }
  
  .main-row, .details-row {
    flex-direction: column;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .vendor-order-container {
    margin-left: 16px;
    margin-right: 16px;
    padding: 16px;
  }
  
  .vendor-order-container.sidebar-collapsed {
    margin-left: 16px;
  }
}