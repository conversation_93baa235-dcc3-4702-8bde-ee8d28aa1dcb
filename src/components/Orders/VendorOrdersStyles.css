/* Vendor Orders Styles */

.vendor-orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px 8px 0 0;
}

.vendor-orders-header .orders-count {
  display: flex;
  gap: 24px;
  font-size: 14px;
  font-weight: 500;
}

.vendor-orders-header .orders-count span {
  color: white;
}

.bulk-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
}

.vendor-orders-table {
  width: 100%;
  background: white;
  border-radius: 0 0 8px 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.vendor-orders-table .ant-table-thead > tr > th {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  color: #4a5568;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
  padding: 16px 12px;
}

.vendor-orders-table .ant-table-tbody > tr:hover > td {
  background-color: #f7fafc;
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* Row styling */
.vendor-table-row-light {
  background-color: #ffffff;
}

.vendor-table-row-dark {
  background-color: #f8fafc;
}

.vendor-orders-table .ant-table-tbody > tr.vendor-table-row-light:hover > td,
.vendor-orders-table .ant-table-tbody > tr.vendor-table-row-dark:hover > td {
  background: linear-gradient(135deg, #667eea15 0%, #764ba215 100%);
  color: #2d3748;
}

/* Order ID styling */
.vendor-order-id {
  font-weight: 600;
  color: #667eea;
  font-family: 'Monaco', 'Menlo', monospace;
  background: #f0f2ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* Customer info styling */
.customer-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.customer-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
}

.customer-email {
  font-size: 12px;
  color: #718096;
  font-style: italic;
}

/* Product info styling */
.product-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.product-name {
  font-weight: 500;
  color: #2d3748;
  font-size: 14px;
}

.product-category {
  font-size: 12px;
  color: #718096;
  background: #edf2f7;
  padding: 2px 6px;
  border-radius: 12px;
  display: inline-block;
  width: fit-content;
}

/* Date and budget info */
.date-info,
.budget-info {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #4a5568;
  font-weight: 500;
}

.budget-info {
  color: #38a169;
  font-weight: 600;
}

/* Priority and status tags */
.priority-tag,
.status-tag {
  font-weight: 600;
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 12px;
  border: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priority-tag {
  min-width: 60px;
  text-align: center;
}

.status-tag {
  min-width: 80px;
  text-align: center;
}

/* Action buttons */
.action-btn {
  border-radius: 6px;
  border: none;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-btn {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  border-color: #3182ce;
}

.view-btn:hover {
  background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(49, 130, 206, 0.3);
}

.approve-btn {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  border-color: #38a169;
}

.approve-btn:hover {
  background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(56, 161, 105, 0.3);
}

.reject-btn {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  border-color: #e53e3e;
}

.reject-btn:hover {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(229, 62, 62, 0.3);
}

.message-btn {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
  border-color: #dd6b20;
  color: white;
}

.message-btn:hover {
  background: linear-gradient(135deg, #dd6b20 0%, #c05621 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(221, 107, 32, 0.3);
  color: white;
}

/* Pagination styling */
.vendor-orders-table .ant-pagination {
  padding: 16px 24px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.vendor-orders-table .ant-pagination .ant-pagination-item-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
}

.vendor-orders-table .ant-pagination .ant-pagination-item-active a {
  color: white;
}

/* Selection styling */
.vendor-orders-table .ant-table-tbody > tr.ant-table-row-selected > td {
  background: linear-gradient(135deg, #667eea20 0%, #764ba220 100%);
}

.vendor-orders-table .ant-table-tbody > tr.ant-table-row-selected:hover > td {
  background: linear-gradient(135deg, #667eea30 0%, #764ba230 100%);
}

/* Loading state */
.vendor-orders-table .ant-spin-container {
  min-height: 400px;
}

/* Responsive design */
@media (max-width: 1200px) {
  .vendor-orders-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .vendor-orders-header .orders-count {
    flex-direction: column;
    gap: 8px;
  }
  
  .action-btn {
    padding: 4px 8px;
  }
}

@media (max-width: 768px) {
  .customer-info,
  .product-info {
    font-size: 12px;
  }
  
  .customer-name,
  .product-name {
    font-size: 13px;
  }
  
  .customer-email,
  .product-category {
    font-size: 11px;
  }
  
  .action-btn {
    padding: 2px 6px;
  }
  
  .vendor-order-id {
    font-size: 11px;
    padding: 2px 6px;
  }
}

/* Animation for new orders */
@keyframes newOrderPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

.vendor-orders-table .ant-table-tbody > tr[data-status="pending"] {
  animation: newOrderPulse 2s infinite;
}

/* Tooltip styling */
.ant-tooltip-inner {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  border-radius: 6px;
  font-weight: 500;
}

.ant-tooltip-arrow::before {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
}
