import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Tag, Modal, message, Tooltip } from 'antd';
import { useNavigate } from 'react-router-dom';
import { 
  CheckOutlined, 
  CloseOutlined, 
  EyeOutlined, 
  MessageOutlined,
  DollarOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import './VendorOrdersStyles.css';

// Utility function to get orders from localStorage
const getOrdersFromStorage = () => {
  try {
    const orders = localStorage.getItem('orders');
    return orders ? JSON.parse(orders) : [];
  } catch (error) {
    console.error('Error getting orders from localStorage:', error);
    return [];
  }
};

const VendorOrdersTable = ({ orders: hardcodedOrders = [], loading, onOrderClick }) => {
  const [allOrders, setAllOrders] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const navigate = useNavigate();

  // Mock vendor-specific orders data
  const vendorMockOrders = [
    {
      id: 'VND001',
      customerName: '<PERSON>',
      customerEmail: '<EMAIL>',
      productName: 'Enterprise Cloud Hosting',
      category: 'Cloud Services',
      requestDate: '28Apr2025',
      dueDate: '29Jun2025',
      status: 'pending',
      priority: 'high',
      budget: '$2,500',
      description: 'Need enterprise-level cloud hosting solution with 99.9% uptime guarantee',
      orderType: 'new'
    },
    {
      id: 'VND002',
      customerName: 'Khalid Ahmed',
      customerEmail: '<EMAIL>',
      productName: 'Web Development',
      category: 'Development Services',
      requestDate: '25Apr2025',
      dueDate: '15May2025',
      status: 'approved',
      priority: 'medium',
      budget: '$1,800',
      description: 'Custom e-commerce website with payment integration',
      orderType: 'new'
    },
    {
      id: 'VND003',
      customerName: 'Sarah Ali',
      customerEmail: '<EMAIL>',
      productName: 'Digital Marketing',
      category: 'Marketing Services',
      requestDate: '20Apr2025',
      dueDate: '30Apr2025',
      status: 'in_progress',
      priority: 'high',
      budget: '$3,200',
      description: 'Complete digital marketing campaign for product launch',
      orderType: 'renewal'
    },
    {
      id: 'VND004',
      customerName: 'Ahmed Mahmoud',
      customerEmail: '<EMAIL>',
      productName: 'Mobile App Development',
      category: 'Development Services',
      requestDate: '18Apr2025',
      dueDate: '18Jul2025',
      status: 'completed',
      priority: 'low',
      budget: '$5,000',
      description: 'iOS and Android mobile application development',
      orderType: 'new'
    },
    {
      id: 'VND005',
      customerName: 'Fatima Nasser',
      customerEmail: '<EMAIL>',
      productName: 'SEO Optimization',
      category: 'Marketing Services',
      requestDate: '15Apr2025',
      dueDate: '15May2025',
      status: 'rejected',
      priority: 'medium',
      budget: '$800',
      description: 'SEO optimization for existing website',
      orderType: 'upgrade'
    }
  ];

  useEffect(() => {
    // Get orders from localStorage
    const storedOrders = getOrdersFromStorage();
    
    // Combine hardcoded orders with stored orders and vendor mock data
    const combinedOrders = [...hardcodedOrders, ...storedOrders, ...vendorMockOrders];
    
    setAllOrders(combinedOrders);
  }, [hardcodedOrders]);

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'orange';
      case 'approved':
        return 'blue';
      case 'in_progress':
        return 'cyan';
      case 'completed':
        return 'green';
      case 'rejected':
        return 'red';
      case 'cancelled':
        return 'default';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'high':
        return 'red';
      case 'medium':
        return 'orange';
      case 'low':
        return 'green';
      default:
        return 'default';
    }
  };

  const handleApproveOrder = (orderId) => {
    Modal.confirm({
      title: 'Approve Order',
      content: 'Are you sure you want to approve this order?',
      okText: 'Approve',
      okType: 'primary',
      cancelText: 'Cancel',
      onOk() {
        // Update order status logic here
        message.success('Order approved successfully!');
        // You would typically make an API call here
      },
    });
  };

  const handleRejectOrder = (orderId) => {
    Modal.confirm({
      title: 'Reject Order',
      content: 'Are you sure you want to reject this order? This action cannot be undone.',
      okText: 'Reject',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        // Update order status logic here
        message.error('Order rejected.');
        // You would typically make an API call here
      },
    });
  };

  const handleViewDetails = (record) => {
    navigate('/vendorOrder', { state: { orderData: record } });
  };

  const handleMessageCustomer = (record) => {
    // Navigate to messaging interface or open modal
    message.info(`Opening message thread with ${record.customerName}`);
  };

  const getRowClassName = (record, index) => {
    return index % 2 === 0 ? 'vendor-table-row-light' : 'vendor-table-row-dark';
  };

  const columns = [
    {
      title: 'Order ID',
      dataIndex: 'id',
      key: 'id',
      width: '10%',
      render: (text) => (
        <span className="vendor-order-id">{text}</span>
      ),
    },
    {
      title: 'Customer',
      key: 'customer',
      width: '15%',
      render: (_, record) => (
        <div className="customer-info">
          <div className="customer-name">{record.customerName}</div>
          <div className="customer-email">{record.customerEmail}</div>
        </div>
      ),
    },
    {
      title: 'Product/Service',
      dataIndex: 'productName',
      key: 'productName',
      width: '20%',
      render: (text, record) => (
        <div className="product-info">
          <div className="product-name">{text}</div>
          <div className="product-category">{record.category}</div>
        </div>
      ),
    },
    {
      title: 'Request Date',
      dataIndex: 'requestDate',
      key: 'requestDate',
      width: '10%',
      render: (date) => (
        <div className="date-info">
          <CalendarOutlined style={{ marginRight: 4, color: '#666' }} />
          {date}
        </div>
      ),
    },
    {
      title: 'Budget',
      dataIndex: 'budget',
      key: 'budget',
      width: '10%',
      render: (budget) => (
        <div className="budget-info">
          <DollarOutlined style={{ marginRight: 4, color: '#52c41a' }} />
          {budget}
        </div>
      ),
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      width: '8%',
      render: (priority) => (
        <Tag color={getPriorityColor(priority)} className="priority-tag">
          {priority?.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: '10%',
      render: (status) => (
        <Tag color={getStatusColor(status)} className="status-tag">
          {status?.replace('_', ' ').toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '17%',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button
              type="primary"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
              className="action-btn view-btn"
            />
          </Tooltip>
          
          {record.status === 'pending' && (
            <>
              <Tooltip title="Approve Order">
                <Button
                  type="primary"
                  size="small"
                  icon={<CheckOutlined />}
                  onClick={() => handleApproveOrder(record.id)}
                  className="action-btn approve-btn"
                />
              </Tooltip>
              <Tooltip title="Reject Order">
                <Button
                  danger
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={() => handleRejectOrder(record.id)}
                  className="action-btn reject-btn"
                />
              </Tooltip>
            </>
          )}
          
          <Tooltip title="Message Customer">
            <Button
              type="default"
              size="small"
              icon={<MessageOutlined />}
              onClick={() => handleMessageCustomer(record)}
              className="action-btn message-btn"
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys) => {
      setSelectedRowKeys(selectedRowKeys);
    },
  };

  const hasSelected = selectedRowKeys.length > 0;

  return (
    <>
      <div className="vendor-orders-header">
        <div className="orders-count">
          <span>{allOrders.length} Orders Found</span>
          <span>{allOrders.filter(order => order.status === 'pending').length} Pending Approval</span>
        </div>
        
        {hasSelected && (
          <div className="bulk-actions">
            <span style={{ marginRight: 8 }}>
              {selectedRowKeys.length} selected
            </span>
            <Space>
              <Button type="primary" size="small">
                Bulk Approve
              </Button>
              <Button danger size="small">
                Bulk Reject
              </Button>
            </Space>
          </div>
        )}
      </div>

      <Table
        className="vendor-orders-table"
        columns={columns}
        dataSource={allOrders}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `${range[0]}-${range[1]} of ${total} orders`,
        }}
        loading={loading}
        size="middle"
        rowClassName={getRowClassName}
        rowSelection={rowSelection}
        scroll={{ x: 1200 }}
      />
    </>
  );
};

export default VendorOrdersTable;
