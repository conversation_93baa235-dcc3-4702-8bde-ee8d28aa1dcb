import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Tag, Modal, message, Tooltip } from 'antd';
import { useNavigate } from 'react-router-dom';
import { 
  CheckOutlined, 
  CloseOutlined, 
  EyeOutlined, 
  MessageOutlined,
  DollarOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import './VendorOrdersStyles.css';

// Utility function to get orders from localStorage
const getOrdersFromStorage = () => {
  try {
    const orders = localStorage.getItem('orders');
    return orders ? JSON.parse(orders) : [];
  } catch (error) {
    console.error('Error getting orders from localStorage:', error);
    return [];
  }
};

const VendorOrdersTable = ({ orders: hardcodedOrders = [], loading, onOrderClick }) => {
  const [allOrders, setAllOrders] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const navigate = useNavigate();

  // Mock vendor-specific orders data
  const vendorMockOrders = [
    {
      id: 'VND001',
      customerName: '<PERSON>',
      customerEmail: '<EMAIL>',
      productName: 'Enterprise Cloud Hosting',
      category: 'Cloud Services',
      requestDate: '2025-04-28',
      expiryDate: '2026-05-05',
      dueDate: '2025-06-29',
      status: 'pending',
      priority: 'high',
      budget: '$2,500',
      description: 'Need enterprise-level cloud hosting solution with 99.9% uptime guarantee',
      orderType: 'new'
    },
    {
      id: 'VND002',
      customerName: 'Khalid Ahmed',
      customerEmail: '<EMAIL>',
      productName: 'Web Development',
      category: 'Development Services',
      requestDate: '2025-04-25',
      expiryDate: '2025-05-02',
      dueDate: '2025-05-15',
      status: 'approved',
      priority: 'medium',
      budget: '$1,800',
      description: 'Custom e-commerce website with payment integration',
      orderType: 'new'
    },
    {
      id: 'VND003',
      customerName: 'Sarah Ali',
      customerEmail: '<EMAIL>',
      productName: 'Digital Marketing',
      category: 'Marketing Services',
      requestDate: '2025-04-20',
      expiryDate: '2025-04-27',
      dueDate: '2025-04-30',
      status: 'in_progress',
      priority: 'high',
      budget: '$3,200',
      description: 'Complete digital marketing campaign for product launch',
      orderType: 'renewal'
    },
    {
      id: 'VND004',
      customerName: 'Ahmed Mahmoud',
      customerEmail: '<EMAIL>',
      productName: 'Mobile App Development',
      category: 'Development Services',
      requestDate: '2025-04-18',
      expiryDate: '2025-04-25',
      dueDate: '2025-07-18',
      status: 'completed',
      priority: 'low',
      budget: '$5,000',
      description: 'iOS and Android mobile application development',
      orderType: 'new'
    },
    {
      id: 'VND005',
      customerName: 'Fatima Nasser',
      customerEmail: '<EMAIL>',
      productName: 'SEO Optimization',
      category: 'Marketing Services',
      requestDate: '2025-04-15',
      expiryDate: '2025-04-22',
      dueDate: '2025-05-15',
      status: 'rejected',
      priority: 'medium',
      budget: '$800',
      description: 'SEO optimization for existing website',
      orderType: 'upgrade'
    }
  ];

  useEffect(() => {
    // For vendors, we only use vendor-specific mock data
    // The hardcodedOrders from parent are customer-focused and not compatible
    // In a real app, this would be replaced with vendor-specific API calls

    // Get orders from localStorage (if any vendor orders were stored)
    const storedOrders = getOrdersFromStorage();

    // Filter stored orders to only include vendor-compatible ones (those with customerName field)
    const vendorStoredOrders = storedOrders.filter(order => order.customerName);

    // Use only vendor mock data and compatible stored orders
    const combinedOrders = [...vendorMockOrders, ...vendorStoredOrders];

    // Debug: Log the data to console
    console.log('VendorOrdersTable - Combined Orders:', combinedOrders);
    console.log('VendorOrdersTable - Sample Order Structure:', combinedOrders[0]);

    setAllOrders(combinedOrders);
  }, [hardcodedOrders]);

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'orange';
      case 'approved':
        return 'blue';
      case 'in_progress':
        return 'cyan';
      case 'completed':
        return 'green';
      case 'rejected':
        return 'red';
      case 'cancelled':
        return 'default';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'high':
        return 'red';
      case 'medium':
        return 'orange';
      case 'low':
        return 'green';
      default:
        return 'default';
    }
  };

  const handleApproveOrder = (orderId) => {
    Modal.confirm({
      title: 'Approve Order',
      content: 'Are you sure you want to approve this order?',
      okText: 'Approve',
      okType: 'primary',
      cancelText: 'Cancel',
      onOk() {
        // Update order status logic here
        message.success('Order approved successfully!');
        // You would typically make an API call here
      },
    });
  };

  const handleRejectOrder = (orderId) => {
    Modal.confirm({
      title: 'Reject Order',
      content: 'Are you sure you want to reject this order? This action cannot be undone.',
      okText: 'Reject',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        // Update order status logic here
        message.error('Order rejected.');
        // You would typically make an API call here
      },
    });
  };

  const handleViewDetails = (record) => {
    navigate('/vendorOrder', { state: { orderData: record } });
  };

  const handleMessageCustomer = (record) => {
    // Navigate to messaging interface or open modal
    message.info(`Opening message thread with ${record.customerName}`);
  };

  const isOrderExpired = (expiryDate) => {
    if (!expiryDate) return false;
    const today = new Date();
    const expiry = new Date(expiryDate);
    return expiry < today;
  };

  const isOrderExpiringSoon = (expiryDate) => {
    if (!expiryDate) return false;
    const today = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 2 && diffDays >= 0; // Expiring within 2 days
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  const getRowClassName = (record, index) => {
    let baseClass = index % 2 === 0 ? 'vendor-table-row-light' : 'vendor-table-row-dark';

    if (isOrderExpired(record.expiryDate)) {
      baseClass += ' expired-row';
    } else if (isOrderExpiringSoon(record.expiryDate)) {
      baseClass += ' expiring-soon-row';
    }

    return baseClass;
  };

  const columns = [
    {
      title: 'Order ID',
      dataIndex: 'id',
      key: 'id',
      width: '10%',
      render: (text) => (
        <span className="vendor-order-id">{text}</span>
      ),
    },
    {
      title: 'Customer',
      key: 'customer',
      width: '15%',
      render: (_, record) => (
        <div className="customer-info">
          <div className="customer-name">{record.customerName}</div>
          <div className="customer-email">{record.customerEmail}</div>
        </div>
      ),
    },
    {
      title: 'Product/Service',
      dataIndex: 'productName',
      key: 'productName',
      width: '20%',
      render: (text, record) => (
        <div className="product-info">
          <div className="product-name">{text}</div>
          <div className="product-category">{record.category}</div>
        </div>
      ),
    },
    {
      title: 'Request Date',
      dataIndex: 'requestDate',
      key: 'requestDate',
      width: '9%',
      render: (date) => (
        <div className="date-info">
          <CalendarOutlined style={{ marginRight: 4, color: '#666' }} />
          {formatDate(date)}
        </div>
      ),
    },
    {
      title: 'Expiry Date',
      dataIndex: 'expiryDate',
      key: 'expiryDate',
      width: '9%',
      render: (date) => {
        const expired = isOrderExpired(date);
        const expiringSoon = isOrderExpiringSoon(date);

        return (
          <div className={`date-info expiry-date ${expired ? 'expired' : expiringSoon ? 'expiring-soon' : ''}`}>
            <CalendarOutlined style={{
              marginRight: 4,
              color: expired ? '#e53e3e' : expiringSoon ? '#f6ad55' : '#f56565'
            }} />
            {formatDate(date)}
            {expired && <span className="expiry-badge expired-badge">EXPIRED</span>}
            {expiringSoon && <span className="expiry-badge expiring-badge">URGENT</span>}
          </div>
        );
      },
    },
    {
      title: 'Budget',
      dataIndex: 'budget',
      key: 'budget',
      width: '10%',
      render: (budget) => (
        <div className="budget-info">
          <DollarOutlined style={{ marginRight: 4, color: '#52c41a' }} />
          {budget}
        </div>
      ),
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      width: '8%',
      render: (priority) => (
        <Tag color={getPriorityColor(priority)} className="priority-tag">
          {priority?.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: '10%',
      render: (status) => (
        <Tag color={getStatusColor(status)} className="status-tag">
          {status?.replace('_', ' ').toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '16%',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button
              type="primary"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
              className="action-btn view-btn"
            />
          </Tooltip>
          
          {record.status === 'pending' && (
            <>
              <Tooltip title="Approve Order">
                <Button
                  type="primary"
                  size="small"
                  icon={<CheckOutlined />}
                  onClick={() => handleApproveOrder(record.id)}
                  className="action-btn approve-btn"
                />
              </Tooltip>
              <Tooltip title="Reject Order">
                <Button
                  danger
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={() => handleRejectOrder(record.id)}
                  className="action-btn reject-btn"
                />
              </Tooltip>
            </>
          )}
          
          <Tooltip title="Message Customer">
            <Button
              type="default"
              size="small"
              icon={<MessageOutlined />}
              onClick={() => handleMessageCustomer(record)}
              className="action-btn message-btn"
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys) => {
      setSelectedRowKeys(selectedRowKeys);
    },
  };

  const hasSelected = selectedRowKeys.length > 0;

  return (
    <>
      <div className="vendor-orders-header">
        <div className="orders-count">
          <span>{allOrders.length} Orders Found</span>
          <span>{allOrders.filter(order => order.status === 'pending').length} Pending Approval</span>
        </div>
        
        {hasSelected && (
          <div className="bulk-actions">
            <span style={{ marginRight: 8 }}>
              {selectedRowKeys.length} selected
            </span>
            <Space>
              <Button type="primary" size="small">
                Bulk Approve
              </Button>
              <Button danger size="small">
                Bulk Reject
              </Button>
            </Space>
          </div>
        )}
      </div>

      <Table
        className="vendor-orders-table"
        columns={columns}
        dataSource={allOrders}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `${range[0]}-${range[1]} of ${total} orders`,
        }}
        loading={loading}
        size="middle"
        rowClassName={getRowClassName}
        rowSelection={rowSelection}
        scroll={{ x: 1200 }}
      />
    </>
  );
};

export default VendorOrdersTable;
