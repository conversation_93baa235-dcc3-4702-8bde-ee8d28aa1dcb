import React, { useState, useEffect } from 'react';
import { Layout } from 'antd';
import { useNavigate } from 'react-router-dom'; // Add this import for navigation
import CustomSider from '../components/Pages/CustomSider';
import PageHeader from '../components/Common/PageHeader';
import OrdersTable from '../components/Orders/OrdersTable';
import '../components/Orders/OrdersStyles.css';

const { Content } = Layout;

const MyOrders = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const navigate = useNavigate(); // Add navigation hook

  // Mock data for demonstration
  const mockOrders = [
    {
      id: 'B2829',
      productName: 'Enterprise Cloud Hosting',
      category: 'Cloud Services',
      vendor: 'ABC Cloud Services',
      status: 'Approved',
      startDate: '28Apr2025',
      endDate: '29Jun2025',
      orderStatus: 'Awarded',
    },
    {
      id: 'B3435',
      productName: 'Web Hosting',
      category: 'Web Services',
      vendor: 'ABC Web Host',
      status: 'Running',
      startDate: '19Apr2025',
      endDate: '29Apr2025',
      orderStatus: 'Cancelled',
    },
    {
      id: 'B2829',
      productName: 'Enterprise Cloud Hosting',
      category: 'Cloud Services',
      vendor: 'ABC Cloud Services',
      status: 'Pending',
      startDate: '28Apr2025',
      endDate: '29Jun2025',
      orderStatus: 'Awarded',
    },
    {
      id: 'B2829',
      productName: 'Enterprise Cloud Hosting',
      category: 'Cloud Services',
      vendor: 'ABC Cloud Services',
      status: 'Received',
      startDate: '28Apr2025',
      endDate: '29Jun2025',
      orderStatus: 'Approve',
    },
    {
      id: 'B3435',
      productName: 'Web Hosting',
      category: 'Web Services',
      vendor: 'ABC Web Host',
      status: 'Expired',
      startDate: '19Apr2025',
      endDate: '29Apr2025',
      orderStatus: 'Cancelled',
    },
    {
      id: 'B2829',
      productName: 'Enterprise Cloud Hosting',
      category: 'Cloud Services',
      vendor: 'ABC Cloud Services',
      status: 'Approved',
      startDate: '28Apr2025',
      endDate: '29Jun2025',
      orderStatus: 'Awarded',
    },
    {
      id: 'B3435',
      productName: 'Web Hosting',
      category: 'Web Services',
      vendor: 'ABC Web Host',
      status: 'Running',
      startDate: '19Apr2025',
      endDate: '29Apr2025',
      orderStatus: 'Cancelled',
    },
    {
      id: 'B2829',
      productName: 'Enterprise Cloud Hosting',
      category: 'Cloud Services',
      vendor: 'ABC Cloud Services',
      status: 'Pending',
      startDate: '28Apr2025',
      endDate: '29Jun2025',
      orderStatus: 'Awarded',
    },
    {
      id: 'B2829',
      productName: 'Enterprise Cloud Hosting',
      category: 'Cloud Services',
      vendor: 'ABC Cloud Services',
      status: 'Received',
      startDate: '28Apr2025',
      endDate: '29Jun2025',
      orderStatus: 'Approve',
    },
    {
      id: 'B3435',
      productName: 'Web Hosting',
      category: 'Web Services',
      vendor: 'ABC Web Host',
      status: 'Expired',
      startDate: '19Apr2025',
      endDate: '29Apr2025',
      orderStatus: 'Cancelled',
    },
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setOrders(mockOrders);
      setFilteredOrders(mockOrders);
      setLoading(false);
    }, 1000);
  }, []);

  const handleSearch = (value) => {
    if (!value) {
      setFilteredOrders(orders);
      return;
    }

    const filtered = orders.filter(
      (order) =>
        order.id.toLowerCase().includes(value.toLowerCase()) ||
        order.productName.toLowerCase().includes(value.toLowerCase()) ||
        order.category.toLowerCase().includes(value.toLowerCase()) ||
        order.vendor.toLowerCase().includes(value.toLowerCase()) ||
        order.status.toLowerCase().includes(value.toLowerCase()) ||
        order.orderStatus.toLowerCase().includes(value.toLowerCase())
    );

    setFilteredOrders(filtered);
  };

  // Add click handler for order rows
  const handleOrderClick = (order) => {
    // You can pass order data through state or URL params
    navigate('/vendorOrder', { state: { orderData: order } });
    
    // Alternative: If you want to pass order ID in URL
    // navigate(`/vendorOrder/${order.id}`);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <CustomSider collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout
        style={{
          marginLeft: collapsed ? '80px' : '220px',
          transition: 'margin-left 0.3s',
        }}
      >
        <Content className="orders-container">
            <PageHeader
              title="My Orders"
              timestamp="Last Updated on Today"
              onSearch={handleSearch}
              showSearch={true}
              userAvatar="https://xsgames.co/randomusers/avatar.php?g=pixel"
            />
          <div className="orders-content">
            <OrdersTable 
              orders={filteredOrders} 
              loading={loading} 
              onOrderClick={handleOrderClick} // Pass the click handler
            />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MyOrders;