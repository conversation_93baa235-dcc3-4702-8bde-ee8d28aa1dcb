#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}🚀 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Set script to exit on any error
set -e

print_status "Starting build process for user-profile-service..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Maven wrapper exists
if [ ! -f "./mvnw" ]; then
    print_error "Maven wrapper (mvnw) not found!"
    exit 1
fi

# Make Maven wrapper executable
chmod +x ./mvnw

# Clean any previous builds
print_status "Cleaning previous builds..."
./mvnw clean || { print_error "Maven clean failed!"; exit 1; }

# Run tests (optional - can be skipped with -s flag)
if [[ "$1" != "-s" && "$1" != "--skip-tests" ]]; then
    print_status "Running tests..."
    ./mvnw test || { print_error "Tests failed!"; exit 1; }
    print_success "All tests passed!"
else
    print_warning "Skipping tests as requested"
fi

# Build the application
print_status "Building application with Maven..."
./mvnw package -DskipTests=true || { print_error "Maven build failed!"; exit 1; }

# Find the built JAR file
JAR_FILE=$(find target -name "user-profile-service-*.jar" | head -n 1)

# Check if JAR file exists
if [ -z "$JAR_FILE" ]; then
    print_error "JAR file not found in target directory!"
    print_status "Available files in target:"
    ls -la target/ || true
    exit 1
fi

print_success "Found JAR file: $JAR_FILE"

# Build the Docker image
print_status "Building Docker image..."
docker build -t user-profile-service:latest . || { print_error "Docker build failed!"; exit 1; }

print_success "Docker image built successfully!"

# Tag the image with version if available
VERSION=$(./mvnw help:evaluate -Dexpression=project.version -q -DforceStdout 2>/dev/null || echo "unknown")
if [ "$VERSION" != "unknown" ]; then
    docker tag user-profile-service:latest user-profile-service:$VERSION
    print_success "Tagged image as user-profile-service:$VERSION"
fi

# Show image info
print_status "Docker image information:"
docker images user-profile-service

print_success "Build completed successfully!"
print_status "To run the service:"
echo "  Single container: docker run -p 8080:8080 user-profile-service:latest"
echo "  With database:    docker-compose up -d"
